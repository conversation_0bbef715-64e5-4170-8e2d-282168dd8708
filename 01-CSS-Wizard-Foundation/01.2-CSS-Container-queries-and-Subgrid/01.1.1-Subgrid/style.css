@layer reset {
    html {
        box-sizing: border-box;
    }
    *,
    *::before,
    *::after {
        box-sizing: inherit;
    }
    body {
        font-family: "Libre Franklin", sans-serif;
        font-size: 1.2rem;
        line-height: 1.5;
        margin: 0;
        padding: 0;
    }
    .one {
        border: 2px solid #26547c;
        background-color: #26547c33;
    }
    .two,
    .five {
        border: 2px solid #ef476f;
        background-color: #ef476f33;
    }
    .three, .four {
        border: 2px solid #ffd166;
        background-color: #ffd16633;
    }
    .button {
        background-color: black;
        padding: 0.5rem;
        border-radius: 5px;
        color: white;
        text-decoration: none;
    }
    .button:hover {
        background-color: #333;
    }
    img {
        max-width: 100%;
    }
}

.container{
    display: grid;
    grid-template-columns: repeat(5, minmax(200px, 1fr));
    grid-template-rows: repeat(3, 1fr);
    gap: 2rem;
    max-width: 1200px;
    margin: 0;

    .one{
        grid-column: 1/3;
        grid-row: 1/7;
    }

    > * {
        padding: 1rem;
        margin: 0;
    }

    .card {
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: subgrid;
        grid-row: span 3;

        > * {
            margin: 0;
        }

    }

}