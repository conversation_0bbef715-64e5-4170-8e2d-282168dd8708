<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>CSS Grid</title>
<!--    <link rel="stylesheet" href="style.css">-->
</head>
<body>

<!--CSS Grid Dev Tools-->
<!--
<div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
    <div class="item">8</div>
    <div class="item">9</div>
    <div class="item">10</div>
</div>
-->

<!--CSS Grid Implicit vs Explicit Tracks-->
<!--
<div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
    <div class="item">8</div>
</div>
-->

<!--CSS grid-auto-flow Explained-->
<!--<div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
</div>-->

<!--Sizing tracks in CSS Grid-->
<!-- <div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
    <div class="item">8</div>
    <div class="item">9</div>
    <div class="item">10</div>
    <div class="item">11</div>
    <div class="item">12</div>
    <div class="item">13</div>
    <div class="item">14</div>
    <div class="item">15</div>
</div> -->

<!--CSS Grid repeat function-->
<!--
<div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
    <div class="item">8</div>
    <div class="item">9</div>
    <div class="item">10</div>
    <div class="item">11</div>
    <div class="item">12</div>
    <div class="item">13</div>
    <div class="item">14</div>
    <div class="item">15</div>
</div>
-->

<!--Sizing Grid items-->
<!--
<div class="container">
    <div class="item item1">1</div>
    <div class="item item2">2</div>
    <div class="item item3">3</div>
    <div class="item item4">4</div>
    <div class="item item5">5</div>
    <div class="item item6">6</div>
    <div class="item item7">7</div>
    <div class="item item8">8</div>
    <div class="item item9">9</div>
    <div class="item item10">10</div>
    <div class="item item11">11</div>
    <div class="item item12">12</div>
    <div class="item item13">13</div>
    <div class="item item14">14</div>
    <div class="item item15">15</div>
    <div class="item item16">16</div>
    <div class="item item17">17</div>
    <div class="item item18">18</div>
    <div class="item item19">19</div>
    <div class="item item20">20</div>
    <div class="item item21">21</div>
    <div class="item item22">22</div>
    <div class="item item23">23</div>
    <div class="item item24">24</div>
    <div class="item item25">25</div>
    <div class="item item26">26</div>
    <div class="item item27">27</div>
    <div class="item item28">28</div>
    <div class="item item29">29</div>
    <div class="item item30">30</div>
</div>
-->

<!--Placing Grid items-->
<!--
<div class="container">
    <div class="item item1">1</div>
    <div class="item item2">2</div>
    <div class="item item3">3</div>
    <div class="item item4">4</div>
    <div class="item item5">5</div>
    <div class="item item6">6</div>
    <div class="item item7">7</div>
    <div class="item item8">8</div>
    <div class="item item9">9</div>
    <div class="item item10">10</div>
    <div class="item item11">11</div>
    <div class="item item12">12</div>
    <div class="item item13">13</div>
    <div class="item item14">14</div>
    <div class="item item15">15</div>
    <div class="item item16">16</div>
    <div class="item item17">17</div>
    <div class="item item18">18</div>
    <div class="item item19">19</div>
    <div class="item item20">20</div>
    <div class="item item21">21</div>
    <div class="item item22">22</div>
    <div class="item item23">23</div>
    <div class="item item24">24</div>
    <div class="item item25">25</div>
    <div class="item item26">26</div>
    <div class="item item27">27</div>
    <div class="item item28">28</div>
    <div class="item item29">29</div>
    <div class="item item30">30</div>
</div>
-->

<!--Full Bleed Blog-->
<!--<article class="post">
    <h2>Chapter Three: The Great American Game</h2>

    <p>Dear Diary,</p>

    <p>Grandfather's creaky as his front porch, loads his shotgun beneath the awning, spittoon restless for rain, carpenter's
        chair against the whistling air. Rocking, back and forth, rocking snap shot picture - worth it, just like the movies</p>

    <div class="tip tip-right">
        <p>This is a song by
            <a href="http://levithepoet.net/">Levi The Poet</a>
        </p>
    </div>


    <p>She said that he loved baseball, and James Earl Jones; said that he's got god talking inside of his thoughts while he's
        rounding those bases on his way back home</p>

    <blockquote>
        <p>"If you build it, they will come"</p>
    </blockquote>

    <p>If you build it, they will come (and baby listens to what the Lord say). But I've been getting pretty worn, building
        for nearly a decade</p>

    <figure>
        <img src="assets/birmingham-museums-trust-ze_aQiu3ZFU-unsplash.jpg" alt="Big Ass Image">
        <figcaption>Super Nice Photo</figcaption>
    </figure>


    <p>In a perfect world we shouldn’t have been allowed to lose sight of what it means to love wholly. I’ve got a Polaroid
        hanging on my wall that a friend took of me and my angel. I remember the day like it’s something I can touch, but it’s
        stuck in the square between the borders of the film, and I can run my fingers over our faces, but I can’t get back
        to the places we were</p>


    <blockquote>
        <p>You’ve got a pain deep in your bones, son.</p>
    </blockquote>
    <p>"You’ve got a pain deep in your bones, son. It compels you forward like you’re tied to a slave master’s cruel hand, and
        it's the same pain that drives that oppressor’s heart of stone, so you’ve grown to love the man. You keep pouring yourself
        out, again and again, into legible lines through a crooked pen." Yeah, it’s painful, but it’s familiar – so habit breeds
        comfort, and I don’t know what I’d do without him</p>

    <div class="tip tip-left">
        <p>You can listen to this song on
            <a href="https://www.youtube.com/watch?v=-gvpej9TT-g">YouTube</a>
        </p>
    </div>

    <p>So in the early morning, when you’ve fallen asleep in our home, I drift back into the memories that I’ve claimed as my
        own, and wonder if tonight will be a night I’ll hang on my wall like I did before we stopped taking photos, out of
        the habit of being comfortable with not trying at all</p>

    <p>In a perfect world, we’ll have albums labeled Seasons, with chapter headings, and we’ll staple them to the cork-board
        that hangs at the foot of the bed. There’s longevity in a memory spilt out in pen, and if a picture is worth a thousand
        words then I’ve written down every one of them</p>

    <p>I work hard, scarred, toil through that soil for the youth I see in my friends, but these journals are moments in time,
        snapshots of our lives, and in retrospect, age is an overexposed photo that the memories can't mend</p>

    <div class="tip tip-right">
        <img src="https://f4.bcbits.com/img/a1057026609_16.jpg" alt="Correspondence Album Cover">
        <p>Buy the album on
            <a href="https://levithepoet.bandcamp.com/album/correspondence-a-fiction">BandCamp</a>
        </p>
    </div>
    <p>I know my sweet seductress, and her name is Depression. I wrote best beneath that demon’s destructive oppression</p>


    <p>In those Polaroids, she drove the ink into the canvas like a slave beneath his master’s cruel hand, and I hated that
        whip but always wondered what I’d do without it, so I grew to love the man</p>

    <p>Oh, I wept for change! I begged for movement and the good Lord, he answered my prayers, but you don’t know how to breathe
        easy when you let go of your habits, even if your comforts left you gasping for air</p>


    <figure>
        <img src="assets/europeana-hlM3UPphM88-unsplash.jpg" alt="Big Ass Image">
        <figcaption>Super Nice Photo</figcaption>
    </figure>

    <h2>Dear Time</h2>

    <p>Grandfather's as creaky as his front porch, scent like oil in the gun barrel, dip-can kicked over the railing, sandpaper
        hands stuck behind thumb tacks on my wall. I’ve got an ache in my chest for every season I miss and it gets worse when
        the snow starts to fall. There are butterflies alive in that couple’s eyes a few years since forgotten by all, and
        sometimes, if the phone starts to ring, I can still hear their wings when you call</p>

    <p>But I begged for movement and I got what I asked for, and I can picture the answer like it came yesterday. And in the
        land of the gods, I think that things are timeless, but we are still prone to decay</p>

    <p>You know I still lift up hope of certain smiles in those photos for us when I pray</p>

    <blockquote>
        <p>"Idle hands build nothing that you can call your own"</p>
    </blockquote>

    <p>Time is a cruel lover, and she breaks her house apart at its bones. You know comfort is no good reason for standing still,
        and idle hands build nothing that you can call your own.</p>

    <blockquote>
        <p>— Levi The Poet</p>
    </blockquote>

</article>-->

<style>
    /*CSS Grid Implicit vs Explicit Tracks*/
    /*
    .container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: 200px 400px;
        grid-template-rows: 50px 100px;
        grid-auto-rows: 500px;

    }
    */

    /*CSS grid-auto-flow Explained*/
    /*.container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: 400px 200px;
        grid-auto-flow: column;
        grid-auto-columns: 200px;

    }
    */

    /*Sizing tracks in CSS Grid*/
    /* .container {
        display: grid;
        height: 600px;
        grid-gap: 24px;
        grid-template-columns: auto 1fr;
        !*grid-template-rows: 1fr 10fr 1fr 5fr 1fr;*!
    }*/

    /*CSS Grid repeat function*/
    /*.container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: repeat(4, 1fr auto);

    }*/

    /*sizing Grid items*/
    /* .container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: repeat(5, 1fr);

        .item9{
            !*width: 500px;*!
            grid-column: span 10;
            grid-row: span 2;
            background-color: red;
        }

    }
    */

    /*Placing Grid items*/
    /*    .container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: repeat(5, 1fr);

        .item9{
            grid-column: 2 / 5;
        }
    }*/

    /*Full Bleed Blog*/
    /*:root {
        --yellow: #ffc600;
    }

    html {
        color: #444;
        font-family: Cambria, Cochin, Georgia, 'Times New Roman', Times, serif;
        font-size: 20px;
    }

    p {
        line-height: 1.8;
    }

    img {
        max-width: 100%;
    }

    a {
        color: black;
        text-decoration: underline wavy var(--yellow);
    }

    h1,
    h2 {
        font-size: 80px;
        font-style: italic;
        font-weight: 100;
        margin: 0;
    }
    .post{
        display: grid;
        max-width: 1000px;
        margin: 200px auto;
        grid-template-columns: 3fr 12fr 5fr;
        grid-gap: 10px 50px;

        > * {
            grid-column: 2 / 3;
        }
        > figure {
            margin: 0;
            grid-column: 1 / 4;
        }
        figcaption{
            font-size: 10px;
        }
        blockquote{
            grid-column: 1 / 4;
            font-size: 60px;
            font-style: italic;
            text-align: center;
            margin: 0;
        }
        .tip{
            background: #FAFAFA;
            padding: 10px;

        }
        .tip-left{
            grid-column: 1 / 2;
            text-align: right;
            border-right: 2px solid var(--yellow) ;
        }
        .tip-right{
            grid-column: 3 / 4;
            border-left: 2px solid var(--yellow) ;
        }
    }*/




</style>

</body>
</html>