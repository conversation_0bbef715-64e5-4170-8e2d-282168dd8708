@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;700&display=swap');

*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    background-color: #f5f5f5;
}

header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20px;
}

header h1 {
    font-weight: 300;
    margin-left: 10px;
}

.container {
    max-width: 500px;
    margin: 30px auto;
    padding: 20px;
}

.edit-mode {
    color: #ccc;
}

/* Form & Input */
.form-input {
    width: 100%;
    font-size: 18px;
    margin-bottom: 20px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    outline: none;
}

.form-input-filter {
    margin-top: 20px;
    width: 100%;
    font-size: 18px;
    margin-bottom: 20px;
    padding: 10px;
    border: none;
    border-bottom: 1px solid #ccc;
    background: transparent;
    outline: none;
}

/* Buttons */
.btn {
    background-color: #333;
    color: #fff;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
}

.btn:hover {
    background-color: #444;
}

.btn-link {
    font-size: 16px;
    background-color: transparent;
    color: #333;
    border: none;
    padding: 0;
    cursor: pointer;
}

.btn-clear {
    margin-top: 20px;
    width: 100%;
    font-size: 16px;
    background-color: transparent;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
}

.btn-clear:hover {
    background-color: #f1f1f1;
}

.text-red {
    color: red;
}

/* Items */

.items {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
}

.items li {
    display: flex;
    justify-content: space-between;
    width: 45%;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px 15px;
    margin: 0 5px 20px;
    font-weight: 700;
    cursor: pointer;
}

@media (max-width: 500px) {
    .items li {
        width: 100%;
    }
}
